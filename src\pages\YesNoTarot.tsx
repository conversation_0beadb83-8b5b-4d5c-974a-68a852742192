import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import { useTheme } from '../contexts/ThemeContext';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import SEO from '../components/SEO';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LoginPrompt from '../components/LoginPrompt';
import VipPromptDialog from '../components/VipPromptDialog';
import { getFontClass } from '../utils/fontUtils';

// 导入拆分后的组件
import YesNoTarotSelectionCards from '../components/yes-no-tarot/YesNoTarotSelectionCards';
import AskingQuestions from '../components/yes-no-tarot/AskingQuestions';
import HowItWorks from '../components/yes-no-tarot/HowItWorks';
import FAQ from '../components/yes-no-tarot/FAQ';
import MoreTarotOptions from '../components/yes-no-tarot/MoreTarotOptions';
import GeneralSpotlightSection from '../components/yes-no-tarot/GeneralSpotlightSection';

const YesNoTarot: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { user } = useUser();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const isDark = theme === 'dark';

  // 检查用户权限
  const checkUserPermission = () => {
    if (!user) {
      setShowLoginPrompt(true);
      return false;
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };

  // 开始单卡占卜
  const handleStartSingleCardReading = () => {
    if (!checkUserPermission()) return;
    // 直接导航到单卡占卜页面
    navigate('/yes-no-tarot/single-card');
  };

  // 开始三卡占卜
  const handleStartThreeCardReading = () => {
    if (!checkUserPermission()) return;
    navigate('/yes-no-tarot/three-cards');
  };

  return (
    <div className="main-container min-h-screen flex flex-col relative">
      <SEO 
      />
      <LandingBackground />
      
      {/* 主要内容 */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          {/* 标题区域 */}
            <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10">
              <div className="space-y-4 sm:space-y-6">
                <h1 className={`${i18n.language === 'en' ? 'text-3xl sm:text-4xl md:text-5xl' : 'text-4xl sm:text-5xl md:text-6xl'} font-bold`}>
                  <div className="text-white">{t('yes_no_tarot.subtitle_obj.part1')}</div>
                  <div className="mt-2">
                    <span style={{color: "#C66CCD"}} className="text-purple-400">{t('yes_no_tarot.subtitle_obj.part2')}</span>
                    <span className="text-white">{i18n.language === 'en' ? ' ' : ''}{t('yes_no_tarot.subtitle_obj.part3')}</span>
                  </div>
                </h1>
                <div className="pt-1 sm:pt-2">
                  <h2 className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto mb-0">
                    {/* 桌面版本 */}
                    <div className="hidden sm:flex sm:items-center sm:justify-center">
                      <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.instant_answers')}</span>
                      <span className="mx-2 text-purple-500">✦</span>
                      <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.intuitive_reading')}</span>
                      <span className="mx-2 text-purple-500">✦</span>
                      <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.yes_no_answers')}</span>
                    </div>
                    
                    {/* 英文版本移动端 - 保持两行布局 */}
                    {i18n.language === 'en' && (
                      <div className="flex flex-col sm:hidden space-y-2">
                        <div className="flex items-center justify-center">
                          <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.instant_answers')}</span>
                          <span className="mx-1 text-purple-500">✦</span>
                          <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.intuitive_reading')}</span>
                        </div>
                        <div className="flex items-center justify-center">
                          <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.yes_no_answers')}</span>
                        </div>
                      </div>
                    )}
                    
                    {/* 非英文版本移动端 - 单行显示 */}
                    {i18n.language !== 'en' && (
                      <div className="flex flex-row sm:hidden items-center justify-center overflow-x-auto whitespace-nowrap px-2">
                        <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.instant_answers')}</span>
                        <span className="mx-1 text-purple-500">✦</span>
                        <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.intuitive_reading')}</span>
                        <span className="mx-1 text-purple-500">✦</span>
                        <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t('yes_no_tarot.features.yes_no_answers')}</span>
                      </div>
                    )}
                  </h2>
              </div>
            </div>
          </div>

          {/* 剩余占卜次数显示组件 */}
          <div className="text-center mb-2 sm:mb-3 mt-2 sm:mt-4">
            {user && (
              <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
                ${isDark
                  ? 'bg-purple-900/30 text-purple-200 border border-purple-500/30 hover:bg-purple-900/40'
                  : 'bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100'
                }`}>
                <svg
                  className={`w-4 h-4 ${isDark ? 'text-purple-300' : 'text-purple-600'}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
                <span>
                  {user.vipStatus === 'active'
                    ? t('common.remaining_reads_vip')
                    : t('common.remaining_reads', { count: user.remainingReads })
                  }
                </span>
                {/* VIP用户显示无限符号，普通用户显示剩余次数提示 */}
                {user.vipStatus !== 'active' && user.remainingReads <= 1 && (
                  <div className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                    user.remainingReads === 0
                      ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                      : 'bg-amber-500/20 text-amber-400 border border-amber-500/30'
                  }`}>
                    {user.remainingReads === 0 ? t('common.reads_exhausted') : t('common.reads_running_low')}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 页面内容 */}
          <div className="max-w-[95%] lg:max-w-5xl mx-auto mt-0 sm:mt-4">
            {/* 使用选择卡片组件 */}
              <YesNoTarotSelectionCards 
                onStartSingleCardReading={handleStartSingleCardReading}
                onStartThreeCardReading={handleStartThreeCardReading}
              />
            
            {/* The Art of Asking 板块 */}
            <AskingQuestions />

      {/* How Our AI Tarot Reader Determines Your Answer 板块 */}
            <HowItWorks />

      {/* FAQ Section */}
            <FAQ />

            {/* 更多塔罗占卜区域 */}
            <MoreTarotOptions onNavigate={navigate} pageType="yesno" />

      {/* 通用SpotlightCard组件 */}
            <GeneralSpotlightSection />
                  </div>
                </div>
            </div>

      <Footer />
      
      {/* VIP提示弹窗 */}
      <VipPromptDialog isOpen={showVipPrompt} onCancel={() => setShowVipPrompt(false)} />

      {/* 登录提示弹窗 */}
      <LoginPrompt isOpen={showLoginPrompt} onClose={() => setShowLoginPrompt(false)} />
    </div>
  );
};

export default YesNoTarot; 